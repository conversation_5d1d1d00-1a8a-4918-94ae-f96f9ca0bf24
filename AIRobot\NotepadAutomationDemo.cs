using System;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using AIRobot.Core.Interfaces;
using AIRobot.Core.Models;
using AIRobot.Extensions;

namespace AIRobot;

/// <summary>
/// 记事本自动化演示程序
/// 完整演示：打开记事本 -> 写入文字 -> 保存文件 -> 关闭程序 -> 移动文件
/// </summary>
class NotepadAutomationDemo
{
    static async Task Main(string[] args)
    {
        Console.WriteLine("🤖 AIRobot 记事本自动化演示");
        Console.WriteLine("========================================");
        Console.WriteLine("将演示完整的自动化流程：");
        Console.WriteLine("1. 打开记事本程序");
        Console.WriteLine("2. 在记事本中写入文字");
        Console.WriteLine("3. 保存文件");
        Console.WriteLine("4. 关闭记事本");
        Console.WriteLine("5. 移动文件到指定位置");
        Console.WriteLine();

        // 创建主机和服务
        var host = Host.CreateDefaultBuilder(args)
            .ConfigureServices((context, services) =>
            {
                services.AddAIRobot();
            })
            .Build();

        try
        {
            var logger = host.Services.GetRequiredService<ILogger<NotepadAutomationDemo>>();
            var platformFactory = host.Services.GetRequiredService<IPlatformServiceFactory>();
            
            // 获取平台特定服务
            var appController = platformFactory.CreateApplicationController();
            var inputSimulator = platformFactory.CreateInputSimulator();
            var screenCapture = platformFactory.CreateScreenCapture();

            logger.LogInformation("🚀 开始记事本自动化演示");
            logger.LogInformation("当前平台: {Platform}", platformFactory.GetPlatformName());

            // 步骤1: 打开记事本
            Console.WriteLine("\n📝 步骤 1: 打开记事本程序...");
            var notepadProcess = await OpenNotepadAsync(appController, logger);
            
            // 等待记事本完全启动
            await Task.Delay(2000);
            
            // 步骤2: 写入文字
            Console.WriteLine("\n✍️ 步骤 2: 在记事本中写入文字...");
            await WriteTextToNotepadAsync(inputSimulator, logger);
            
            // 步骤3: 保存文件
            Console.WriteLine("\n💾 步骤 3: 保存文件...");
            var savedFilePath = await SaveNotepadFileAsync(inputSimulator, logger);
            
            // 步骤4: 关闭记事本
            Console.WriteLine("\n❌ 步骤 4: 关闭记事本...");
            await CloseNotepadAsync(inputSimulator, notepadProcess, logger);
            
            // 步骤5: 移动文件
            Console.WriteLine("\n📁 步骤 5: 移动文件到指定位置...");
            await MoveFileAsync(savedFilePath, logger);
            
            // 验证结果
            Console.WriteLine("\n✅ 步骤 6: 验证自动化操作结果...");
            await VerifyResultsAsync(logger);

            Console.WriteLine("\n🎉 记事本自动化演示完成！");
            Console.WriteLine("所有操作都通过 AIRobot 程序自动完成。");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 自动化演示失败: {ex.Message}");
            Console.WriteLine($"详细错误: {ex}");
        }
        finally
        {
            await host.StopAsync();
        }

        Console.WriteLine("\n按任意键退出...");
        Console.ReadKey();
    }

    /// <summary>
    /// 打开记事本程序
    /// </summary>
    static async Task<Process> OpenNotepadAsync(IApplicationController appController, ILogger logger)
    {
        try
        {
            logger.LogInformation("正在启动记事本程序...");
            
            // 使用 Windows 系统路径启动记事本
            var notepadPath = "notepad.exe";
            var process = await appController.LaunchApplicationAsync(notepadPath);
            
            logger.LogInformation("记事本启动成功，进程ID: {ProcessId}", process.Id);
            Console.WriteLine($"✅ 记事本已启动 (PID: {process.Id})");
            
            return process;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "启动记事本失败");
            throw new InvalidOperationException("无法启动记事本程序", ex);
        }
    }

    /// <summary>
    /// 在记事本中写入文字
    /// </summary>
    static async Task WriteTextToNotepadAsync(IInputSimulator inputSimulator, ILogger logger)
    {
        try
        {
            logger.LogInformation("开始在记事本中写入文字...");
            
            var testText = $"AIRobot 自动化测试\n" +
                          $"===================\n" +
                          $"测试时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}\n" +
                          $"测试内容: 这是通过 AIRobot 程序自动输入的文字\n" +
                          $"功能验证: 屏幕捕获、输入模拟、应用程序控制\n" +
                          $"平台: Windows\n" +
                          $"框架: .NET 9\n\n" +
                          $"🤖 AIRobot 跨平台自动化机器人测试成功！";

            await inputSimulator.SendKeysAsync(testText);
            
            logger.LogInformation("文字写入完成，共 {Length} 个字符", testText.Length);
            Console.WriteLine($"✅ 已写入 {testText.Length} 个字符");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "写入文字失败");
            throw new InvalidOperationException("无法在记事本中写入文字", ex);
        }
    }

    /// <summary>
    /// 保存记事本文件
    /// </summary>
    static async Task<string> SaveNotepadFileAsync(IInputSimulator inputSimulator, ILogger logger)
    {
        try
        {
            logger.LogInformation("开始保存文件...");

            // 使用 Ctrl+S 保存文件
            await inputSimulator.SendKeyComboAsync(VirtualKey.Control, VirtualKey.S);
            await Task.Delay(1000); // 等待保存对话框出现

            // 输入文件名
            var fileName = $"AIRobot_Test_{DateTime.Now:yyyyMMdd_HHmmss}.txt";
            await inputSimulator.SendKeysAsync(fileName);
            await Task.Delay(500);

            // 按回车确认保存
            await inputSimulator.SendKeyAsync(VirtualKey.Return);
            await Task.Delay(1000);

            var savedPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), fileName);
            logger.LogInformation("文件保存完成: {FilePath}", savedPath);
            Console.WriteLine($"✅ 文件已保存: {fileName}");

            return savedPath;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "保存文件失败");
            throw new InvalidOperationException("无法保存记事本文件", ex);
        }
    }

    /// <summary>
    /// 关闭记事本程序
    /// </summary>
    static async Task CloseNotepadAsync(IInputSimulator inputSimulator, Process notepadProcess, ILogger logger)
    {
        try
        {
            logger.LogInformation("开始关闭记事本...");

            // 使用 Alt+F4 关闭程序
            await inputSimulator.SendKeyComboAsync(VirtualKey.Alt, VirtualKey.F4);
            await Task.Delay(1000);

            // 等待程序关闭
            var timeout = DateTime.Now.AddSeconds(5);
            while (!notepadProcess.HasExited && DateTime.Now < timeout)
            {
                await Task.Delay(100);
            }

            if (notepadProcess.HasExited)
            {
                logger.LogInformation("记事本已成功关闭");
                Console.WriteLine("✅ 记事本已关闭");
            }
            else
            {
                logger.LogWarning("记事本未能正常关闭，尝试强制关闭");
                notepadProcess.Kill();
                Console.WriteLine("⚠️ 记事本已强制关闭");
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "关闭记事本失败");
            throw new InvalidOperationException("无法关闭记事本程序", ex);
        }
    }

    /// <summary>
    /// 移动文件到指定位置
    /// </summary>
    static async Task MoveFileAsync(string sourceFilePath, ILogger logger)
    {
        try
        {
            logger.LogInformation("开始移动文件...");

            // 创建目标目录
            var targetDir = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), "AIRobot_Test");
            Directory.CreateDirectory(targetDir);

            var targetFilePath = Path.Combine(targetDir, Path.GetFileName(sourceFilePath));

            // 移动文件
            if (File.Exists(sourceFilePath))
            {
                File.Move(sourceFilePath, targetFilePath);
                logger.LogInformation("文件移动完成: {SourcePath} -> {TargetPath}", sourceFilePath, targetFilePath);
                Console.WriteLine($"✅ 文件已移动到: {targetFilePath}");
            }
            else
            {
                logger.LogWarning("源文件不存在: {SourcePath}", sourceFilePath);
                Console.WriteLine($"⚠️ 源文件不存在: {sourceFilePath}");
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "移动文件失败");
            throw new InvalidOperationException("无法移动文件", ex);
        }
    }

    /// <summary>
    /// 验证自动化操作结果
    /// </summary>
    static async Task VerifyResultsAsync(ILogger logger)
    {
        try
        {
            logger.LogInformation("开始验证操作结果...");

            var targetDir = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), "AIRobot_Test");

            if (Directory.Exists(targetDir))
            {
                var files = Directory.GetFiles(targetDir, "AIRobot_Test_*.txt");
                if (files.Length > 0)
                {
                    var latestFile = files[0];
                    var content = await File.ReadAllTextAsync(latestFile);

                    logger.LogInformation("验证成功 - 找到文件: {FilePath}", latestFile);
                    logger.LogInformation("文件内容长度: {Length} 字符", content.Length);

                    Console.WriteLine("✅ 验证成功！");
                    Console.WriteLine($"   文件位置: {latestFile}");
                    Console.WriteLine($"   文件大小: {new FileInfo(latestFile).Length} 字节");
                    Console.WriteLine($"   内容长度: {content.Length} 字符");

                    // 显示文件内容的前几行
                    var lines = content.Split('\n');
                    Console.WriteLine("   文件内容预览:");
                    for (int i = 0; i < Math.Min(3, lines.Length); i++)
                    {
                        Console.WriteLine($"     {lines[i]}");
                    }
                }
                else
                {
                    logger.LogWarning("目标目录中未找到测试文件");
                    Console.WriteLine("⚠️ 未找到测试文件");
                }
            }
            else
            {
                logger.LogWarning("目标目录不存在");
                Console.WriteLine("⚠️ 目标目录不存在");
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "验证操作结果失败");
            Console.WriteLine($"❌ 验证失败: {ex.Message}");
        }
    }
}
