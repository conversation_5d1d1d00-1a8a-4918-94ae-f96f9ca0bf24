<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🤖 AIRobot 实时自动化控制台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .log-entry {
            border-left: 4px solid #dee2e6;
            padding: 8px 12px;
            margin-bottom: 8px;
            background-color: #f8f9fa;
            border-radius: 4px;
            animation: fadeIn 0.3s ease-in;
        }
        .log-entry.info { border-left-color: #0dcaf0; background-color: #e7f3ff; }
        .log-entry.success { border-left-color: #198754; background-color: #e8f5e8; }
        .log-entry.warning { border-left-color: #ffc107; background-color: #fff8e1; }
        .log-entry.error { border-left-color: #dc3545; background-color: #ffeaea; }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .status-card {
            transition: all 0.3s ease;
        }
        
        .status-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .progress-bar {
            transition: width 0.5s ease;
        }
        
        .pulse {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center py-3 border-bottom">
                    <h1 class="h3 mb-0">🤖 AIRobot 实时自动化控制台</h1>
                    <div class="d-flex gap-2">
                        <span class="badge bg-primary">Windows</span>
                        <span class="badge bg-success" id="status">运行中</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-3">
            <!-- 任务控制面板 -->
            <div class="col-md-4">
                <div class="card status-card">
                    <div class="card-header">
                        <h5><i class="bi bi-play-circle"></i> 任务控制</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">选择任务:</label>
                            <select class="form-select" id="taskSelect">
                                <option value="notepad">记事本自动化演示</option>
                                <option value="calculator">计算器自动化</option>
                                <option value="custom">自定义任务</option>
                            </select>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button class="btn btn-success" id="startBtn" onclick="startTask()">
                                <i class="bi bi-play-fill"></i> 开始任务
                            </button>
                            <button class="btn btn-warning" id="pauseBtn" onclick="pauseTask()" disabled>
                                <i class="bi bi-pause-fill"></i> 暂停任务
                            </button>
                            <button class="btn btn-danger" id="stopBtn" onclick="stopTask()" disabled>
                                <i class="bi bi-stop-fill"></i> 停止任务
                            </button>
                        </div>
                        
                        <div class="mt-3" id="progressContainer" style="display: none;">
                            <div class="progress">
                                <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                     id="progressBar" style="width: 0%">0%</div>
                            </div>
                            <small class="text-muted" id="currentStep">准备中...</small>
                        </div>
                    </div>
                </div>
                
                <!-- 系统状态 -->
                <div class="card status-card mt-3">
                    <div class="card-header">
                        <h5><i class="bi bi-pc-display"></i> 系统状态</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6">
                                <strong>平台:</strong><br>
                                <span class="badge bg-primary">Windows</span>
                            </div>
                            <div class="col-6">
                                <strong>焦点窗口:</strong><br>
                                <span class="badge bg-info" id="focusedWindow">记事本</span>
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-6">
                                <strong>鼠标位置:</strong><br>
                                <code id="mousePos">500, 300</code>
                            </div>
                            <div class="col-6">
                                <strong>活动进程:</strong><br>
                                <span class="badge bg-secondary" id="processCount">156</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 实时操作日志 -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header d-flex justify-content-between">
                        <h5><i class="bi bi-journal-text"></i> 实时操作日志</h5>
                        <button class="btn btn-sm btn-outline-secondary" onclick="clearLogs()">
                            <i class="bi bi-trash"></i> 清空日志
                        </button>
                    </div>
                    <div class="card-body" style="height: 400px; overflow-y: auto;" id="logContainer">
                        <!-- 日志将在这里动态添加 -->
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 当前步骤详情 -->
        <div class="row mt-3">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-zoom-in"></i> 当前步骤详情</h5>
                    </div>
                    <div class="card-body" id="stepDetails">
                        <p class="text-muted">暂无正在执行的步骤</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let isTaskRunning = false;
        let currentProgress = 0;
        let stepIndex = 0;
        let taskInterval;
        
        // 记事本自动化步骤
        const notepadSteps = [
            { name: "启动记事本程序", action: "launch", target: "notepad.exe", duration: 2000 },
            { name: "等待程序完全启动", action: "wait", duration: 1000 },
            { name: "输入测试文字", action: "type", content: "AIRobot 自动化演示\\n时间: " + new Date().toLocaleString(), duration: 3000 },
            { name: "保存文件 (Ctrl+S)", action: "keycombo", keys: "Ctrl+S", duration: 1500 },
            { name: "输入文件名", action: "type", content: "AIRobot_Demo_" + Date.now() + ".txt", duration: 1000 },
            { name: "确认保存 (Enter)", action: "key", key: "Enter", duration: 1000 },
            { name: "关闭记事本 (Alt+F4)", action: "keycombo", keys: "Alt+F4", duration: 1000 },
            { name: "验证文件已保存", action: "verify", duration: 500 }
        ];
        
        function addLog(operation, message, level = 'info', details = '') {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();
            
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${level}`;
            logEntry.innerHTML = `
                <div class="d-flex justify-content-between align-items-start">
                    <div class="flex-grow-1">
                        <span class="badge bg-${getBadgeColor(level)}">${level.toUpperCase()}</span>
                        <strong>${operation}</strong>
                        <p class="mb-1">${message}</p>
                        ${details ? `<small class="text-muted">${details}</small>` : ''}
                    </div>
                    <small class="text-muted">${timestamp}</small>
                </div>
            `;
            
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        function getBadgeColor(level) {
            switch(level) {
                case 'success': return 'success';
                case 'warning': return 'warning';
                case 'error': return 'danger';
                default: return 'info';
            }
        }
        
        function updateProgress(progress, stepName) {
            const progressBar = document.getElementById('progressBar');
            const currentStep = document.getElementById('currentStep');
            
            progressBar.style.width = progress + '%';
            progressBar.textContent = progress + '%';
            currentStep.textContent = stepName;
        }
        
        function updateStepDetails(step) {
            const stepDetails = document.getElementById('stepDetails');
            stepDetails.innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>步骤信息</h6>
                        <table class="table table-sm">
                            <tr><td><strong>步骤名称:</strong></td><td>${step.name}</td></tr>
                            <tr><td><strong>操作类型:</strong></td><td>${step.action}</td></tr>
                            <tr><td><strong>执行状态:</strong></td><td><span class="badge bg-primary pulse">执行中</span></td></tr>
                            <tr><td><strong>预计耗时:</strong></td><td>${step.duration}ms</td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>目标信息</h6>
                        <table class="table table-sm">
                            <tr><td><strong>目标程序:</strong></td><td>${step.target || '记事本'}</td></tr>
                            <tr><td><strong>输入内容:</strong></td><td>${step.content || step.keys || step.key || '-'}</td></tr>
                            <tr><td><strong>焦点窗口:</strong></td><td>记事本</td></tr>
                        </table>
                    </div>
                </div>
            `;
        }
        
        async function startTask() {
            if (isTaskRunning) return;
            
            isTaskRunning = true;
            stepIndex = 0;
            
            document.getElementById('startBtn').disabled = true;
            document.getElementById('pauseBtn').disabled = false;
            document.getElementById('stopBtn').disabled = false;
            document.getElementById('progressContainer').style.display = 'block';
            
            addLog('任务控制', '开始执行记事本自动化任务', 'info', '共8个步骤');
            
            await executeSteps();
        }
        
        async function executeSteps() {
            for (let i = 0; i < notepadSteps.length; i++) {
                if (!isTaskRunning) break;
                
                const step = notepadSteps[i];
                const progress = Math.round(((i + 1) / notepadSteps.length) * 100);
                
                updateProgress(progress, step.name);
                updateStepDetails(step);
                
                addLog('步骤执行', `开始: ${step.name}`, 'info', `操作: ${step.action}`);
                
                // 模拟步骤执行
                await new Promise(resolve => setTimeout(resolve, step.duration));
                
                addLog('步骤执行', `完成: ${step.name}`, 'success', `耗时: ${step.duration}ms`);
                
                // 更新系统状态
                updateSystemStatus(step);
            }
            
            if (isTaskRunning) {
                addLog('任务控制', '记事本自动化任务执行完成', 'success', '所有步骤均已成功执行');
                stopTask();
            }
        }
        
        function updateSystemStatus(step) {
            // 模拟更新系统状态
            const mousePos = document.getElementById('mousePos');
            const focusedWindow = document.getElementById('focusedWindow');
            
            mousePos.textContent = `${Math.floor(Math.random() * 1920)}, ${Math.floor(Math.random() * 1080)}`;
            
            if (step.action === 'launch') {
                focusedWindow.textContent = '记事本';
                focusedWindow.className = 'badge bg-success';
            }
        }
        
        function pauseTask() {
            addLog('任务控制', '任务已暂停', 'warning');
        }
        
        function stopTask() {
            isTaskRunning = false;
            
            document.getElementById('startBtn').disabled = false;
            document.getElementById('pauseBtn').disabled = true;
            document.getElementById('stopBtn').disabled = true;
            document.getElementById('progressContainer').style.display = 'none';
            
            document.getElementById('stepDetails').innerHTML = '<p class="text-muted">暂无正在执行的步骤</p>';
            
            addLog('任务控制', '任务已停止', 'warning');
        }
        
        function clearLogs() {
            document.getElementById('logContainer').innerHTML = '';
            addLog('系统', '日志已清空', 'info');
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            addLog('系统', 'AIRobot 实时自动化控制台已启动', 'info', '平台: Windows, 框架: .NET 9');
            addLog('系统', '所有核心服务已就绪', 'success', '屏幕捕获、输入模拟、应用程序控制');
            
            // 模拟定期更新系统状态
            setInterval(() => {
                if (!isTaskRunning) {
                    const processCount = document.getElementById('processCount');
                    processCount.textContent = Math.floor(Math.random() * 50) + 150;
                }
            }, 3000);
        });
    </script>
</body>
</html>
