using System;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using System.Windows.Forms;
using Microsoft.Extensions.Logging;

namespace AIRobot.WinForms;

public partial class SimpleAIRobotDemo : Form
{
    private ILogger<SimpleAIRobotDemo> logger;
    private bool isTaskRunning = false;
    
    // UI控件
    private Button startButton;
    private Button screenshotButton;
    private Button clearButton;
    private ListBox logListBox;
    private PictureBox screenshotPictureBox;
    private Label statusLabel;
    private ProgressBar progressBar;

    public SimpleAIRobotDemo()
    {
        InitializeComponent();
        InitializeServices();
        AddInitialLogs();
    }

    private void InitializeComponent()
    {
        this.Text = "🤖 AIRobot 实时自动化控制台";
        this.Size = new Size(1200, 800);
        this.StartPosition = FormStartPosition.CenterScreen;
        this.Icon = SystemIcons.Application;
        this.BackColor = Color.FromArgb(248, 249, 250);
        
        // 创建主面板
        var mainPanel = new TableLayoutPanel
        {
            Dock = DockStyle.Fill,
            ColumnCount = 2,
            RowCount = 1,
            Padding = new Padding(10)
        };
        mainPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 30F));
        mainPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 70F));
        
        // 左侧控制面板
        var leftPanel = CreateControlPanel();
        mainPanel.Controls.Add(leftPanel, 0, 0);
        
        // 右侧日志和截图面板
        var rightPanel = CreateLogPanel();
        mainPanel.Controls.Add(rightPanel, 1, 0);
        
        this.Controls.Add(mainPanel);
    }
    
    private Panel CreateControlPanel()
    {
        var panel = new Panel
        {
            Dock = DockStyle.Fill,
            BackColor = Color.White,
            Padding = new Padding(15)
        };
        
        var titleLabel = new Label
        {
            Text = "🎮 任务控制",
            Font = new Font("Microsoft YaHei", 12, FontStyle.Bold),
            ForeColor = Color.FromArgb(33, 37, 41),
            AutoSize = true,
            Location = new Point(15, 15)
        };
        
        var taskLabel = new Label
        {
            Text = "选择任务:",
            Font = new Font("Microsoft YaHei", 9),
            AutoSize = true,
            Location = new Point(15, 50)
        };
        
        var taskComboBox = new ComboBox
        {
            Text = "记事本自动化演示",
            Font = new Font("Microsoft YaHei", 9),
            Size = new Size(250, 25),
            Location = new Point(15, 75),
            DropDownStyle = ComboBoxStyle.DropDownList
        };
        taskComboBox.Items.Add("记事本自动化演示");
        taskComboBox.SelectedIndex = 0;
        
        startButton = new Button
        {
            Text = "▶ 开始任务",
            Font = new Font("Microsoft YaHei", 10, FontStyle.Bold),
            Size = new Size(120, 40),
            Location = new Point(15, 120),
            BackColor = Color.FromArgb(40, 167, 69),
            ForeColor = Color.White,
            FlatStyle = FlatStyle.Flat,
            UseVisualStyleBackColor = false
        };
        startButton.Click += StartButton_Click;
        
        screenshotButton = new Button
        {
            Text = "📷 截图",
            Font = new Font("Microsoft YaHei", 10),
            Size = new Size(120, 40),
            Location = new Point(145, 120),
            BackColor = Color.FromArgb(23, 162, 184),
            ForeColor = Color.White,
            FlatStyle = FlatStyle.Flat,
            UseVisualStyleBackColor = false
        };
        screenshotButton.Click += ScreenshotButton_Click;
        
        progressBar = new ProgressBar
        {
            Size = new Size(250, 20),
            Location = new Point(15, 180),
            Visible = false
        };
        
        statusLabel = new Label
        {
            Text = "就绪",
            Font = new Font("Microsoft YaHei", 9),
            AutoSize = true,
            Location = new Point(15, 210),
            ForeColor = Color.FromArgb(108, 117, 125)
        };
        
        var systemLabel = new Label
        {
            Text = "🖥️ 系统状态",
            Font = new Font("Microsoft YaHei", 12, FontStyle.Bold),
            ForeColor = Color.FromArgb(33, 37, 41),
            AutoSize = true,
            Location = new Point(15, 250)
        };
        
        var platformLabel = new Label
        {
            Text = "平台: Windows (.NET 9)",
            Font = new Font("Microsoft YaHei", 9),
            AutoSize = true,
            Location = new Point(15, 280),
            ForeColor = Color.FromArgb(108, 117, 125)
        };
        
        var timeLabel = new Label
        {
            Text = $"时间: {DateTime.Now:HH:mm:ss}",
            Font = new Font("Microsoft YaHei", 9),
            AutoSize = true,
            Location = new Point(15, 305),
            ForeColor = Color.FromArgb(108, 117, 125)
        };
        
        // 定时更新时间
        var timer = new Timer { Interval = 1000 };
        timer.Tick += (s, e) => timeLabel.Text = $"时间: {DateTime.Now:HH:mm:ss}";
        timer.Start();
        
        panel.Controls.AddRange(new Control[] {
            titleLabel, taskLabel, taskComboBox, startButton, screenshotButton,
            progressBar, statusLabel, systemLabel, platformLabel, timeLabel
        });
        
        return panel;
    }
    
    private Panel CreateLogPanel()
    {
        var panel = new Panel
        {
            Dock = DockStyle.Fill,
            BackColor = Color.White,
            Padding = new Padding(15)
        };
        
        var titleLabel = new Label
        {
            Text = "📝 实时操作日志",
            Font = new Font("Microsoft YaHei", 12, FontStyle.Bold),
            ForeColor = Color.FromArgb(33, 37, 41),
            AutoSize = true,
            Location = new Point(15, 15)
        };
        
        clearButton = new Button
        {
            Text = "🗑️ 清空",
            Font = new Font("Microsoft YaHei", 9),
            Size = new Size(80, 30),
            Location = new Point(panel.Width - 100, 10),
            BackColor = Color.FromArgb(108, 117, 125),
            ForeColor = Color.White,
            FlatStyle = FlatStyle.Flat,
            UseVisualStyleBackColor = false,
            Anchor = AnchorStyles.Top | AnchorStyles.Right
        };
        clearButton.Click += ClearButton_Click;
        
        logListBox = new ListBox
        {
            Font = new Font("Consolas", 9),
            Size = new Size(panel.Width - 30, 300),
            Location = new Point(15, 50),
            Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Bottom,
            BackColor = Color.FromArgb(248, 249, 250),
            BorderStyle = BorderStyle.FixedSingle
        };
        
        var screenshotLabel = new Label
        {
            Text = "📸 实时截图",
            Font = new Font("Microsoft YaHei", 12, FontStyle.Bold),
            ForeColor = Color.FromArgb(33, 37, 41),
            AutoSize = true,
            Location = new Point(15, 370),
            Anchor = AnchorStyles.Bottom | AnchorStyles.Left
        };
        
        screenshotPictureBox = new PictureBox
        {
            Size = new Size(panel.Width - 30, 200),
            Location = new Point(15, 400),
            Anchor = AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right,
            BorderStyle = BorderStyle.FixedSingle,
            SizeMode = PictureBoxSizeMode.Zoom,
            BackColor = Color.FromArgb(248, 249, 250)
        };
        
        panel.Controls.AddRange(new Control[] {
            titleLabel, clearButton, logListBox, screenshotLabel, screenshotPictureBox
        });
        
        return panel;
    }

    private void InitializeServices()
    {
        using var loggerFactory = LoggerFactory.Create(builder =>
        {
            builder.AddConsole().SetMinimumLevel(LogLevel.Information);
        });

        logger = loggerFactory.CreateLogger<SimpleAIRobotDemo>();
    }
    
    private void AddInitialLogs()
    {
        AddLog("系统", "AIRobot 实时自动化控制台已启动", "INFO");
        AddLog("系统", "所有核心服务已就绪", "SUCCESS");
        AddLog("系统", "平台: Windows (.NET 9)", "INFO");
    }
    
    private void AddLog(string operation, string message, string level)
    {
        var timestamp = DateTime.Now.ToString("HH:mm:ss");
        var logEntry = $"[{timestamp}] [{level}] {operation}: {message}";
        
        if (logListBox.InvokeRequired)
        {
            logListBox.Invoke(new Action(() => {
                logListBox.Items.Add(logEntry);
                logListBox.TopIndex = logListBox.Items.Count - 1;
            }));
        }
        else
        {
            logListBox.Items.Add(logEntry);
            logListBox.TopIndex = logListBox.Items.Count - 1;
        }
    }
    
    private async void StartButton_Click(object sender, EventArgs e)
    {
        if (isTaskRunning) return;
        
        await StartNotepadAutomation();
    }
    
    private async void ScreenshotButton_Click(object sender, EventArgs e)
    {
        await CaptureScreenshot();
    }
    
    private void ClearButton_Click(object sender, EventArgs e)
    {
        logListBox.Items.Clear();
        AddLog("系统", "日志已清空", "INFO");
    }

    private async Task StartNotepadAutomation()
    {
        if (isTaskRunning) return;

        try
        {
            isTaskRunning = true;
            startButton.Enabled = false;
            progressBar.Visible = true;
            statusLabel.Text = "执行中...";

            AddLog("任务控制", "开始记事本自动化任务", "INFO");

            // 步骤1: 启动记事本
            progressBar.Value = 10;
            AddLog("步骤执行", "启动记事本程序", "INFO");
            var process = Process.Start("notepad.exe");
            await Task.Delay(2000);
            AddLog("步骤执行", $"记事本已启动 (PID: {process.Id})", "SUCCESS");

            // 步骤2: 输入文字
            progressBar.Value = 30;
            AddLog("步骤执行", "开始输入测试文字", "INFO");
            var testText = $"AIRobot 实时自动化演示\n" +
                          $"========================\n" +
                          $"演示时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}\n" +
                          $"这是通过AIRobot可视化界面执行的自动化任务！\n\n" +
                          $"功能验证:\n" +
                          $"✅ 应用程序启动控制\n" +
                          $"✅ 键盘输入模拟\n" +
                          $"✅ 文件保存操作\n" +
                          $"✅ 程序关闭控制\n\n" +
                          $"🎉 AIRobot 自动化演示成功！";

            SendKeys.SendWait(testText);
            AddLog("步骤执行", $"文字输入完成 ({testText.Length} 字符)", "SUCCESS");
            await Task.Delay(1000);

            // 步骤3: 保存文件
            progressBar.Value = 60;
            AddLog("步骤执行", "保存文件 (Ctrl+S)", "INFO");
            SendKeys.SendWait("^s");
            await Task.Delay(1500);

            var fileName = $"AIRobot_Demo_{DateTime.Now:yyyyMMdd_HHmmss}.txt";
            SendKeys.SendWait(fileName);
            await Task.Delay(500);
            SendKeys.SendWait("{ENTER}");
            await Task.Delay(1000);
            AddLog("步骤执行", $"文件已保存: {fileName}", "SUCCESS");

            // 步骤4: 关闭记事本
            progressBar.Value = 80;
            AddLog("步骤执行", "关闭记事本 (Alt+F4)", "INFO");
            SendKeys.SendWait("%{F4}");
            await Task.Delay(1000);

            // 等待程序关闭
            var timeout = DateTime.Now.AddSeconds(5);
            while (!process.HasExited && DateTime.Now < timeout)
            {
                await Task.Delay(100);
            }

            progressBar.Value = 100;
            if (process.HasExited)
            {
                AddLog("步骤执行", "记事本已成功关闭", "SUCCESS");
            }
            else
            {
                process.Kill();
                AddLog("步骤执行", "记事本已强制关闭", "WARNING");
            }

            AddLog("任务控制", "记事本自动化任务执行完成", "SUCCESS");
            statusLabel.Text = "任务完成";
        }
        catch (Exception ex)
        {
            AddLog("任务控制", $"任务执行失败: {ex.Message}", "ERROR");
            statusLabel.Text = "任务失败";
        }
        finally
        {
            isTaskRunning = false;
            startButton.Enabled = true;
            progressBar.Visible = false;
            progressBar.Value = 0;
        }
    }

    private async Task CaptureScreenshot()
    {
        try
        {
            AddLog("屏幕捕获", "正在捕获屏幕...", "INFO");

            var bounds = Screen.PrimaryScreen.Bounds;
            using (var bitmap = new Bitmap(bounds.Width, bounds.Height))
            {
                using (var graphics = Graphics.FromImage(bitmap))
                {
                    graphics.CopyFromScreen(bounds.X, bounds.Y, 0, 0, bounds.Size);
                }

                screenshotPictureBox.Image?.Dispose();
                screenshotPictureBox.Image = new Bitmap(bitmap);
            }

            AddLog("屏幕捕获", "截图成功", "SUCCESS");
        }
        catch (Exception ex)
        {
            AddLog("屏幕捕获", $"截图失败: {ex.Message}", "ERROR");
        }
    }
}
