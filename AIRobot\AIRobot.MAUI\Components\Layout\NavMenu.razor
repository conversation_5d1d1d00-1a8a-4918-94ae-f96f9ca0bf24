<div class="top-row ps-3 navbar navbar-dark">
    <div class="container-fluid">
        <a class="navbar-brand" href="">AIRobot</a>
        <button title="Navigation menu" class="navbar-toggler" @onclick="ToggleNavMenu">
            <span class="navbar-toggler-icon"></span>
        </button>
    </div>
</div>

<div class="@NavMenuCssClass nav-scrollable" @onclick="ToggleNavMenu">
    <nav class="flex-column">
        <div class="nav-item px-3">
            <NavLink class="nav-link" href="" Match="NavLinkMatch.All">
                <span class="bi bi-house-door-fill-nav-menu" aria-hidden="true"></span> 首页
            </NavLink>
        </div>
        <div class="nav-item px-3">
            <NavLink class="nav-link" href="screen-capture">
                <span class="bi bi-camera-fill" aria-hidden="true"></span> 屏幕捕获
            </NavLink>
        </div>
        <div class="nav-item px-3">
            <NavLink class="nav-link" href="ocr">
                <span class="bi bi-eye-fill" aria-hidden="true"></span> OCR识别
            </NavLink>
        </div>
        <div class="nav-item px-3">
            <NavLink class="nav-link" href="automation">
                <span class="bi bi-gear-fill" aria-hidden="true"></span> 自动化任务
            </NavLink>
        </div>
        <div class="nav-item px-3">
            <NavLink class="nav-link" href="realtime-automation">
                <span class="bi bi-play-circle-fill" aria-hidden="true"></span> 实时自动化
            </NavLink>
        </div>
        <div class="nav-item px-3">
            <NavLink class="nav-link" href="settings">
                <span class="bi bi-sliders" aria-hidden="true"></span> 设置
            </NavLink>
        </div>
    </nav>
</div>

@code {
    private bool collapseNavMenu = true;

    private string? NavMenuCssClass => collapseNavMenu ? "collapse" : null;

    private void ToggleNavMenu()
    {
        collapseNavMenu = !collapseNavMenu;
    }
}
