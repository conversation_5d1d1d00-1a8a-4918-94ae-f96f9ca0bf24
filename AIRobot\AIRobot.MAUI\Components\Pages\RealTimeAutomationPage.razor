@page "/realtime-automation"
@using AIRobot.Core.Interfaces
@using AIRobot.Core.Models
@using AIRobot.Core.Models.Steps
@using System.Diagnostics
@inject IAutomationEngine AutomationEngine
@inject IPlatformServiceFactory PlatformFactory
@inject IJSRuntime JSRuntime

<PageTitle>实时自动化任务</PageTitle>

<h1>🤖 实时自动化任务控制台</h1>
<p>可视化展示所有自动化操作的详细过程</p>

<div class="row">
    <!-- 任务控制面板 -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5>📋 任务控制</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label">选择预设任务:</label>
                    <select class="form-select" @bind="selectedTaskType">
                        <option value="notepad">记事本自动化</option>
                        <option value="calculator">计算器自动化</option>
                        <option value="custom">自定义任务</option>
                    </select>
                </div>
                
                <div class="d-grid gap-2">
                    <button class="btn btn-success" @onclick="StartTask" disabled="@isTaskRunning">
                        <span class="bi bi-play-fill"></span> 开始任务
                    </button>
                    <button class="btn btn-warning" @onclick="PauseTask" disabled="@(!isTaskRunning)">
                        <span class="bi bi-pause-fill"></span> 暂停任务
                    </button>
                    <button class="btn btn-danger" @onclick="StopTask" disabled="@(!isTaskRunning)">
                        <span class="bi bi-stop-fill"></span> 停止任务
                    </button>
                </div>
                
                @if (isTaskRunning)
                {
                    <div class="mt-3">
                        <div class="progress">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                 style="width: @(currentProgress)%">
                                @currentProgress%
                            </div>
                        </div>
                        <small class="text-muted">当前步骤: @currentStepName</small>
                    </div>
                }
            </div>
        </div>
        
        <!-- 系统状态 -->
        <div class="card mt-3">
            <div class="card-header">
                <h5>🖥️ 系统状态</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <strong>平台:</strong><br>
                        <span class="badge bg-primary">@platformName</span>
                    </div>
                    <div class="col-6">
                        <strong>焦点窗口:</strong><br>
                        <span class="badge bg-info">@focusedWindow</span>
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-6">
                        <strong>鼠标位置:</strong><br>
                        <code>@mousePosition</code>
                    </div>
                    <div class="col-6">
                        <strong>活动进程:</strong><br>
                        <span class="badge bg-secondary">@activeProcessCount</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 实时操作日志 -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between">
                <h5>📝 实时操作日志</h5>
                <button class="btn btn-sm btn-outline-secondary" @onclick="ClearLogs">
                    <span class="bi bi-trash"></span> 清空日志
                </button>
            </div>
            <div class="card-body" style="height: 400px; overflow-y: auto;" id="logContainer">
                @foreach (var log in operationLogs.TakeLast(50))
                {
                    <div class="log-entry @GetLogCssClass(log.Level)" data-timestamp="@log.Timestamp.ToString("HH:mm:ss.fff")">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <span class="badge @GetLogBadgeClass(log.Level)">@log.Level</span>
                                <strong>@log.Operation</strong>
                                <p class="mb-1">@log.Message</p>
                                @if (!string.IsNullOrEmpty(log.Details))
                                {
                                    <small class="text-muted">@log.Details</small>
                                }
                            </div>
                            <small class="text-muted">@log.Timestamp.ToString("HH:mm:ss")</small>
                        </div>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

<!-- 详细步骤展示 -->
<div class="row mt-3">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>🔍 当前步骤详情</h5>
            </div>
            <div class="card-body">
                @if (currentStepDetails != null)
                {
                    <div class="row">
                        <div class="col-md-6">
                            <h6>步骤信息</h6>
                            <table class="table table-sm">
                                <tr><td><strong>步骤名称:</strong></td><td>@currentStepDetails.Name</td></tr>
                                <tr><td><strong>步骤类型:</strong></td><td>@currentStepDetails.Type</td></tr>
                                <tr><td><strong>执行状态:</strong></td><td><span class="badge @GetStatusBadgeClass(currentStepDetails.Status)">@currentStepDetails.Status</span></td></tr>
                                <tr><td><strong>开始时间:</strong></td><td>@currentStepDetails.StartTime?.ToString("HH:mm:ss.fff")</td></tr>
                                <tr><td><strong>耗时:</strong></td><td>@currentStepDetails.Duration?.TotalMilliseconds ms</td></tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6>目标信息</h6>
                            <table class="table table-sm">
                                <tr><td><strong>目标进程:</strong></td><td>@currentStepDetails.TargetProcess</td></tr>
                                <tr><td><strong>目标窗口:</strong></td><td>@currentStepDetails.TargetWindow</td></tr>
                                <tr><td><strong>坐标位置:</strong></td><td>@currentStepDetails.TargetPosition</td></tr>
                                <tr><td><strong>输入内容:</strong></td><td>@currentStepDetails.InputContent</td></tr>
                                <tr><td><strong>验证结果:</strong></td><td>@currentStepDetails.VerificationResult</td></tr>
                            </table>
                        </div>
                    </div>
                }
                else
                {
                    <p class="text-muted">暂无正在执行的步骤</p>
                }
            </div>
        </div>
    </div>
</div>

<!-- 截图预览 -->
@if (!string.IsNullOrEmpty(latestScreenshot))
{
    <div class="row mt-3">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5>📸 实时截图</h5>
                </div>
                <div class="card-body text-center">
                    <img src="@latestScreenshot" class="img-fluid" style="max-height: 300px;" alt="实时截图" />
                    <p class="mt-2 text-muted">最后更新: @screenshotTimestamp</p>
                </div>
            </div>
        </div>
    </div>
}

<style>
    .log-entry {
        border-left: 4px solid #dee2e6;
        padding: 8px 12px;
        margin-bottom: 8px;
        background-color: #f8f9fa;
        border-radius: 4px;
    }

    .log-entry.info {
        border-left-color: #0dcaf0;
        background-color: #e7f3ff;
    }

    .log-entry.success {
        border-left-color: #198754;
        background-color: #e8f5e8;
    }

    .log-entry.warning {
        border-left-color: #ffc107;
        background-color: #fff8e1;
    }

    .log-entry.error {
        border-left-color: #dc3545;
        background-color: #ffeaea;
    }
</style>

@code {
    // 任务控制状态
    private bool isTaskRunning = false;
    private string selectedTaskType = "notepad";
    private int currentProgress = 0;
    private string currentStepName = "";
    private CancellationTokenSource? taskCancellationToken;

    // 系统状态
    private string platformName = "";
    private string focusedWindow = "未知";
    private string mousePosition = "0, 0";
    private int activeProcessCount = 0;

    // 操作日志
    private List<OperationLog> operationLogs = new();
    private Timer? statusUpdateTimer;

    // 当前步骤详情
    private StepDetails? currentStepDetails;

    // 截图
    private string latestScreenshot = "";
    private string screenshotTimestamp = "";

    protected override async Task OnInitializedAsync()
    {
        platformName = PlatformFactory.GetPlatformName();
        AddLog("系统", "AIRobot 实时自动化控制台已启动", $"平台: {platformName}", LogLevel.Info);

        // 启动状态更新定时器
        statusUpdateTimer = new Timer(async _ => await UpdateSystemStatus(), null, TimeSpan.Zero, TimeSpan.FromSeconds(2));

        await UpdateSystemStatus();
    }

    private async Task StartTask()
    {
        try
        {
            isTaskRunning = true;
            currentProgress = 0;
            taskCancellationToken = new CancellationTokenSource();

            AddLog("任务控制", $"开始执行任务: {GetTaskDisplayName()}", "", LogLevel.Info);

            var task = CreateTaskByType();
            await ExecuteTaskWithRealTimeUpdates(task);
        }
        catch (Exception ex)
        {
            AddLog("任务控制", "任务执行失败", ex.Message, LogLevel.Error);
        }
        finally
        {
            isTaskRunning = false;
            currentProgress = 100;
            StateHasChanged();
        }
    }

    private async Task PauseTask()
    {
        AddLog("任务控制", "任务已暂停", "", LogLevel.Warning);
        // 实现暂停逻辑
    }

    private async Task StopTask()
    {
        taskCancellationToken?.Cancel();
        isTaskRunning = false;
        currentProgress = 0;
        currentStepName = "";
        currentStepDetails = null;

        AddLog("任务控制", "任务已停止", "", LogLevel.Warning);
        StateHasChanged();
    }

    private AutomationTask CreateTaskByType()
    {
        return selectedTaskType switch
        {
            "notepad" => CreateNotepadTask(),
            "calculator" => CreateCalculatorTask(),
            _ => CreateCustomTask()
        };
    }

    private AutomationTask CreateNotepadTask()
    {
        return new AutomationTask
        {
            Id = Guid.NewGuid().ToString(),
            Name = "记事本自动化演示",
            Description = "完整的记事本自动化流程：启动->输入->保存->关闭",
            Steps = new List<AutomationStep>
            {
                new ClickStep
                {
                    Name = "启动记事本",
                    Description = "通过应用程序控制器启动记事本",
                    Position = new System.Drawing.Point(0, 0), // 占位符
                    Order = 1
                },
                new TypeTextStep
                {
                    Name = "输入测试文字",
                    Text = $"AIRobot 实时自动化演示\n时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}\n这是通过AIRobot可视化界面执行的自动化任务！",
                    Order = 2
                },
                new ClickStep
                {
                    Name = "保存文件",
                    Description = "使用Ctrl+S保存文件",
                    Position = new System.Drawing.Point(0, 0), // 占位符
                    Order = 3
                },
                new TypeTextStep
                {
                    Name = "输入文件名",
                    Text = $"AIRobot_Demo_{DateTime.Now:yyyyMMdd_HHmmss}.txt",
                    Order = 4
                },
                new ClickStep
                {
                    Name = "确认保存",
                    Description = "按回车确认保存",
                    Position = new System.Drawing.Point(0, 0), // 占位符
                    Order = 5
                },
                new ClickStep
                {
                    Name = "关闭记事本",
                    Description = "使用Alt+F4关闭记事本",
                    Position = new System.Drawing.Point(0, 0), // 占位符
                    Order = 6
                }
            }
        };
    }

    private AutomationTask CreateCalculatorTask()
    {
        return new AutomationTask
        {
            Id = Guid.NewGuid().ToString(),
            Name = "计算器自动化演示",
            Description = "启动计算器并执行简单计算",
            Steps = new List<AutomationStep>
            {
                new ClickStep { Name = "启动计算器", Position = new System.Drawing.Point(0, 0), Order = 1 },
                new ClickStep { Name = "点击数字1", Position = new System.Drawing.Point(0, 0), Order = 2 },
                new ClickStep { Name = "点击加号", Position = new System.Drawing.Point(0, 0), Order = 3 },
                new ClickStep { Name = "点击数字2", Position = new System.Drawing.Point(0, 0), Order = 4 },
                new ClickStep { Name = "点击等号", Position = new System.Drawing.Point(0, 0), Order = 5 }
            }
        };
    }

    private AutomationTask CreateCustomTask()
    {
        return new AutomationTask
        {
            Id = Guid.NewGuid().ToString(),
            Name = "自定义任务",
            Description = "用户自定义的自动化任务",
            Steps = new List<AutomationStep>()
        };
    }

    private async Task ExecuteTaskWithRealTimeUpdates(AutomationTask task)
    {
        var totalSteps = task.Steps.Count();
        var currentStep = 0;

        foreach (var step in task.Steps.OrderBy(s => s.Order))
        {
            if (taskCancellationToken?.Token.IsCancellationRequested == true)
                break;

            currentStep++;
            currentProgress = (int)((double)currentStep / totalSteps * 100);
            currentStepName = step.Name;

            // 更新当前步骤详情
            currentStepDetails = new StepDetails
            {
                Name = step.Name,
                Type = step.GetType().Name,
                Status = "准备中",
                StartTime = DateTime.Now,
                TargetProcess = await GetFocusedProcessName(),
                TargetWindow = await GetFocusedWindowTitle()
            };

            StateHasChanged();

            AddLog("步骤执行", $"开始执行: {step.Name}", step.Description ?? "", LogLevel.Info);

            try
            {
                // 更新状态为执行中
                currentStepDetails.Status = "执行中";
                StateHasChanged();

                // 捕获执行前截图
                await CaptureScreenshot();

                // 执行具体步骤
                await ExecuteStepWithDetails(step);

                // 更新状态为完成
                currentStepDetails.Status = "已完成";
                currentStepDetails.Duration = DateTime.Now - currentStepDetails.StartTime;
                currentStepDetails.VerificationResult = "执行成功";

                AddLog("步骤执行", $"完成: {step.Name}", $"耗时: {currentStepDetails.Duration?.TotalMilliseconds:F0}ms", LogLevel.Success);
            }
            catch (Exception ex)
            {
                currentStepDetails.Status = "失败";
                currentStepDetails.Duration = DateTime.Now - currentStepDetails.StartTime;
                currentStepDetails.VerificationResult = $"执行失败: {ex.Message}";

                AddLog("步骤执行", $"失败: {step.Name}", ex.Message, LogLevel.Error);
                throw;
            }

            StateHasChanged();
            await Task.Delay(1000); // 步骤间延迟
        }

        currentStepDetails = null;
        AddLog("任务控制", "任务执行完成", $"共执行 {currentStep} 个步骤", LogLevel.Success);
    }

    private async Task ExecuteStepWithDetails(AutomationStep step)
    {
        var appController = PlatformFactory.CreateApplicationController();
        var inputSimulator = PlatformFactory.CreateInputSimulator();

        switch (step)
        {
            case ClickStep clickStep when clickStep.Name.Contains("启动记事本"):
                var process = await appController.LaunchApplicationAsync("notepad.exe");
                currentStepDetails.TargetProcess = process.ProcessName;
                currentStepDetails.TargetWindow = "记事本";
                await Task.Delay(2000); // 等待启动
                break;

            case TypeTextStep typeStep:
                await inputSimulator.SendKeysAsync(typeStep.Text);
                currentStepDetails.InputContent = typeStep.Text.Length > 50 ?
                    typeStep.Text.Substring(0, 50) + "..." : typeStep.Text;
                break;

            case ClickStep clickStep when clickStep.Name.Contains("保存"):
                await inputSimulator.SendKeyComboAsync(VirtualKey.Control, VirtualKey.S);
                await Task.Delay(1000);
                break;

            case ClickStep clickStep when clickStep.Name.Contains("确认"):
                await inputSimulator.SendKeyAsync(VirtualKey.Return);
                await Task.Delay(1000);
                break;

            case ClickStep clickStep when clickStep.Name.Contains("关闭"):
                await inputSimulator.SendKeyComboAsync(VirtualKey.Alt, VirtualKey.F4);
                await Task.Delay(1000);
                break;

            default:
                await Task.Delay(500); // 默认延迟
                break;
        }
    }

    private async Task UpdateSystemStatus()
    {
        try
        {
            // 更新焦点窗口
            focusedWindow = await GetFocusedWindowTitle();

            // 更新活动进程数
            activeProcessCount = Process.GetProcesses().Length;

            // 更新鼠标位置（模拟）
            mousePosition = $"{Random.Shared.Next(0, 1920)}, {Random.Shared.Next(0, 1080)}";

            await InvokeAsync(StateHasChanged);
        }
        catch (Exception ex)
        {
            // 忽略状态更新错误
        }
    }

    private async Task<string> GetFocusedWindowTitle()
    {
        try
        {
            var screenCapture = PlatformFactory.CreateScreenCapture();
            var windows = await screenCapture.GetVisibleWindowsAsync();
            var focusedWindow = windows.FirstOrDefault(w => !string.IsNullOrEmpty(w.Title));
            return focusedWindow?.Title ?? "未知窗口";
        }
        catch
        {
            return "未知窗口";
        }
    }

    private async Task<string> GetFocusedProcessName()
    {
        try
        {
            var screenCapture = PlatformFactory.CreateScreenCapture();
            var windows = await screenCapture.GetVisibleWindowsAsync();
            var focusedWindow = windows.FirstOrDefault(w => !string.IsNullOrEmpty(w.ProcessName));
            return focusedWindow?.ProcessName ?? "未知进程";
        }
        catch
        {
            return "未知进程";
        }
    }

    private async Task CaptureScreenshot()
    {
        try
        {
            var screenCapture = PlatformFactory.CreateScreenCapture();
            var screenshot = await screenCapture.CaptureScreenAsync();

            // 转换为Base64字符串用于显示
            latestScreenshot = $"data:image/png;base64,{Convert.ToBase64String(screenshot)}";
            screenshotTimestamp = DateTime.Now.ToString("HH:mm:ss");

            AddLog("屏幕捕获", "截图已更新", $"图像大小: {screenshot.Length:N0} 字节", LogLevel.Info);
        }
        catch (Exception ex)
        {
            AddLog("屏幕捕获", "截图失败", ex.Message, LogLevel.Error);
        }
    }

    private void AddLog(string operation, string message, string details, LogLevel level)
    {
        operationLogs.Add(new OperationLog
        {
            Timestamp = DateTime.Now,
            Operation = operation,
            Message = message,
            Details = details,
            Level = level
        });

        // 保持日志数量在合理范围内
        if (operationLogs.Count > 200)
        {
            operationLogs.RemoveRange(0, 50);
        }

        InvokeAsync(StateHasChanged);
        InvokeAsync(ScrollToBottomOfLogs);
    }

    private async Task ScrollToBottomOfLogs()
    {
        try
        {
            await JSRuntime.InvokeVoidAsync("scrollToBottom", "logContainer");
        }
        catch
        {
            // 忽略JS调用错误
        }
    }

    private void ClearLogs()
    {
        operationLogs.Clear();
        AddLog("系统", "日志已清空", "", LogLevel.Info);
    }

    private string GetTaskDisplayName()
    {
        return selectedTaskType switch
        {
            "notepad" => "记事本自动化",
            "calculator" => "计算器自动化",
            _ => "自定义任务"
        };
    }

    private string GetLogCssClass(LogLevel level)
    {
        return level switch
        {
            LogLevel.Info => "info",
            LogLevel.Success => "success",
            LogLevel.Warning => "warning",
            LogLevel.Error => "error",
            _ => ""
        };
    }

    private string GetLogBadgeClass(LogLevel level)
    {
        return level switch
        {
            LogLevel.Info => "bg-info",
            LogLevel.Success => "bg-success",
            LogLevel.Warning => "bg-warning",
            LogLevel.Error => "bg-danger",
            _ => "bg-secondary"
        };
    }

    private string GetStatusBadgeClass(string status)
    {
        return status switch
        {
            "准备中" => "bg-secondary",
            "执行中" => "bg-primary",
            "已完成" => "bg-success",
            "失败" => "bg-danger",
            _ => "bg-secondary"
        };
    }

    public void Dispose()
    {
        statusUpdateTimer?.Dispose();
        taskCancellationToken?.Dispose();
    }

    // 数据模型
    public class OperationLog
    {
        public DateTime Timestamp { get; set; }
        public string Operation { get; set; } = "";
        public string Message { get; set; } = "";
        public string Details { get; set; } = "";
        public LogLevel Level { get; set; }
    }

    public class StepDetails
    {
        public string Name { get; set; } = "";
        public string Type { get; set; } = "";
        public string Status { get; set; } = "";
        public DateTime? StartTime { get; set; }
        public TimeSpan? Duration { get; set; }
        public string TargetProcess { get; set; } = "";
        public string TargetWindow { get; set; } = "";
        public string TargetPosition { get; set; } = "";
        public string InputContent { get; set; } = "";
        public string VerificationResult { get; set; } = "";
    }

    public enum LogLevel
    {
        Info,
        Success,
        Warning,
        Error
    }
}
